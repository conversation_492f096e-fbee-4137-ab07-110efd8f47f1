"use client";

import Link from "next/link";
import { Button } from "./ui/button";
import { usePathname } from "next/navigation";
import { locales, localeNames } from "@/i18n/routing";
import { Menu, Globe, Sparkles } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { signIn, signOut, useSession } from "next-auth/react";
import Image from "next/image";
import { useState, useEffect, useRef } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ThemeToggle } from "@/components/ThemeToggle";

interface HeaderProps {
  header: {
    logo: string;
    nav: {
      faq: string;
    };
    cta: {
      login: string;
      signup: string;
    };
    userMenu: {
      myOrders: string;
      signOut: string;
      profile: string;
    };
  };
}

export default function Header({ header }: HeaderProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('scroll', handleScroll);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const switchLocale = (locale: string) => {
    const newPathname = pathname.replace(`/${currentLocale}`, `/${locale}`);
    window.location.href = newPathname;
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-pink-200/20'
        : 'bg-white/95 backdrop-blur-md'
    }`}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 w-full">
        <nav className="relative flex h-16 items-center justify-between">
          {/* Left: Logo */}
          <div className="flex-none">
            <Link href={`/${currentLocale}`} className="flex items-center group">
              <div className="w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                <Sparkles className="w-4 h-4 text-yellow-300" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent font-serif">
                {header.logo}
              </span>
            </Link>
          </div>

          {/* Center: Navigation Links - Desktop Only */}
          <div className="hidden md:flex items-center justify-center flex-1 px-8">
            <div className="flex space-x-1">
              <button
                onClick={() => scrollToSection('home')}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
              >
                {currentLocale === 'zh' ? '首页' : 'Home'}
              </button>
              <button
                onClick={() => scrollToSection('features')}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
              >
                {currentLocale === 'zh' ? '功能特色' : 'Features'}
              </button>
              <button
                onClick={() => scrollToSection('how-it-works')}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
              >
                {currentLocale === 'zh' ? '使用方法' : 'How It Works'}
              </button>
              <button
                onClick={() => scrollToSection('gallery')}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
              >
                {currentLocale === 'zh' ? '作品展示' : 'Gallery'}
              </button>
              <button
                onClick={() => scrollToSection('pricing')}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
              >
                {currentLocale === 'zh' ? '价格方案' : 'Pricing'}
              </button>
              <button
                onClick={() => scrollToSection('faq')}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
              >
                {header.nav.faq}
              </button>
              <Link
                href={`/${currentLocale}/upload`}
                className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 rounded-full transition-all duration-300 transform hover:scale-105 shadow-md ml-2"
              >
                {currentLocale === 'zh' ? '立即体验' : 'Try Now'}
              </Link>
            </div>
          </div>

          {/* Right: Language & Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            {/* <ThemeToggle /> */}
            <DropdownMenu>
              <DropdownMenuTrigger className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white">
                <Globe className="h-4 w-4" />
                <span>{localeNames[currentLocale]}</span>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {locales.map((locale) => (
                  <DropdownMenuItem
                    key={locale}
                    onClick={() => switchLocale(locale)}
                    className="cursor-pointer"
                  >
                    {localeNames[locale]}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="flex items-center space-x-3">
            {session ? (
              <div className="relative" ref={dropdownRef}>
                <div>
                  <button
                    type="button"
                    className="flex items-center rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  >
                    <span className="sr-only">Open user menu</span>
                    {session.user?.image ? (
                      <Image
                        className="h-8 w-8 rounded-full"
                        src={session.user.image}
                        alt={session.user.name || ''}
                        width={32}
                        height={32}
                        unoptimized
                        priority
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-600">
                          {session.user?.name?.charAt(0) || '?'}
                        </span>
                      </div>
                    )}
                  </button>
                </div>

                {isDropdownOpen && (
                  <div className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5">
                    <div className="px-4 py-2 text-sm text-gray-700">
                      <div className="font-medium">{session.user?.name}</div>
                    </div>
                    <div className="px-4 py-2 text-sm text-gray-700">
                      <div className="text-gray-500">{session.user?.email}</div>
                    </div>
                    <div className="border-t border-gray-100" />
                    <Link
                      href={`/${currentLocale}/orders`}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-pink-50"
                    >
                      {header.userMenu.myOrders}
                    </Link>
                    <Link
                      href={`/${currentLocale}/profile`}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-pink-50"
                    >
                      {header.userMenu.profile}
                    </Link>
                    <button
                      type="button"
                      onClick={() => signOut({ callbackUrl: `/${currentLocale}` })}
                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-pink-50"
                    >
                      {header.userMenu.signOut}
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center">
                <Button
                  onClick={() => signIn()}
                  size="sm"
                  className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white rounded-full px-6 shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                >
                  {header.cta.login}
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Menu Button */}
        <div className="flex md:hidden">
          {/* <ThemeToggle /> */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="h-9 w-9">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <SheetHeader>
                <SheetTitle>{header.logo}</SheetTitle>
              </SheetHeader>
              <div className="flex flex-col space-y-4 mt-6">
                <button
                  onClick={() => scrollToSection('home')}
                  className="text-sm text-gray-600 hover:text-gray-900 text-left"
                >
                  {currentLocale === 'zh' ? '首页' : 'Home'}
                </button>
                <button
                  onClick={() => scrollToSection('features')}
                  className="text-sm text-gray-600 hover:text-gray-900 text-left"
                >
                  {currentLocale === 'zh' ? '功能特色' : 'Features'}
                </button>
                <button
                  onClick={() => scrollToSection('how-it-works')}
                  className="text-sm text-gray-600 hover:text-gray-900 text-left"
                >
                  {currentLocale === 'zh' ? '使用方法' : 'How It Works'}
                </button>
                <button
                  onClick={() => scrollToSection('gallery')}
                  className="text-sm text-gray-600 hover:text-gray-900 text-left"
                >
                  {currentLocale === 'zh' ? '作品展示' : 'Gallery'}
                </button>
                <button
                  onClick={() => scrollToSection('pricing')}
                  className="text-sm text-gray-600 hover:text-gray-900 text-left"
                >
                  {currentLocale === 'zh' ? '价格方案' : 'Pricing'}
                </button>
                <button
                  onClick={() => scrollToSection('faq')}
                  className="text-sm text-gray-600 hover:text-gray-900 text-left"
                >
                  {header.nav.faq}
                </button>
                <Link
                  href={`/${currentLocale}/upload`}
                  className="text-sm font-medium text-pink-600 hover:text-pink-700"
                >
                  {currentLocale === 'zh' ? '立即体验' : 'Try Now'}
                </Link>
                <div className="flex items-center space-x-1 text-sm text-gray-600">
                  <Globe className="h-4 w-4" />
                  <span>{localeNames[currentLocale]}</span>
                </div>
                {session ? (
                  <div className="flex flex-col space-y-3 pt-4">
                    <div className="font-medium">{session.user?.name}</div>
                    <div className="text-gray-500">{session.user?.email}</div>
                    <Link
                      href={`/${currentLocale}/orders`}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-pink-50"
                    >
                      {header.userMenu.myOrders}
                    </Link>
                    <Link
                      href={`/${currentLocale}/profile`}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-pink-50"
                    >
                      {header.userMenu.profile}
                    </Link>
                    <button
                      type="button"
                      onClick={() => signOut({ callbackUrl: `/${currentLocale}` })}
                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-pink-50"
                    >
                      {header.userMenu.signOut}
                    </button>
                  </div>
                ) : (
                  <div className="flex flex-col space-y-3 pt-4">
                    <Button
                      onClick={() => signIn()}
                      size="sm"
                      className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white rounded-full shadow-md"
                    >
                      {header.cta.login}
                    </Button>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
          </div>
        </nav>
      </div>
    </header>
  );
}