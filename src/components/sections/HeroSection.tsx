"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { usePathname } from "next/navigation";
import { Spark<PERSON>, Play, ArrowRight } from "lucide-react";
import Image from "next/image";

interface HeroSectionProps {
  hero: {
    title: string;
    subtitle: string;
    description: string;
    cta: {
      primary: string;
      secondary: string;
    };
  };
  stats: {
    title: string;
    subtitle: string;
    stats: Array<{
      value: string;
      label: string;
      description: string;
    }>;
  };
}

export default function HeroSection({ hero, stats }: HeroSectionProps) {
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-pink-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
      {/* Background Elements - 参考原型的浮动形状 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-[10%] left-[10%] w-48 h-48 bg-gradient-to-br from-pink-400/20 to-purple-600/20 rounded-full animate-float"></div>
        <div className="absolute top-[60%] right-[10%] w-36 h-36 bg-gradient-to-br from-purple-400/20 to-pink-600/20 rounded-full animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-[20%] left-[20%] w-24 h-24 bg-gradient-to-br from-pink-300/20 to-purple-500/20 rounded-full animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="text-center lg:text-left">
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-pink-100 to-purple-100 dark:from-pink-900/30 dark:to-purple-900/30 rounded-full text-sm font-medium text-pink-700 dark:text-pink-300 mb-6">
              <Sparkles className="w-4 h-4 mr-2" />
              {currentLocale === 'zh' ? 'AI驱动的专业婚纱摄影' : 'AI-Powered Professional Photography'}
            </div>
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
              <span className="bg-gradient-to-r from-pink-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                {hero.title}
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
              {hero.subtitle}
            </p>
            
            <p className="text-lg text-gray-500 dark:text-gray-400 mb-8 leading-relaxed">
              {hero.description}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-12">
              <Link href={`/${currentLocale}/upload`}>
                <Button 
                  size="lg" 
                  className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  <Sparkles className="w-5 h-5 mr-2" />
                  {hero.cta.primary}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              
              <Button 
                variant="outline" 
                size="lg"
                onClick={() => scrollToSection('gallery')}
                className="border-2 border-pink-300 text-pink-600 hover:bg-pink-50 dark:border-pink-600 dark:text-pink-400 dark:hover:bg-pink-900/20 px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300"
              >
                <Play className="w-5 h-5 mr-2" />
                {hero.cta.secondary}
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {stats.stats.map((stat, index) => (
                <div key={index} className="text-center lg:text-left">
                  <div className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                    {stat.value}
                  </div>
                  <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Visual */}
          <div className="relative">
            <div className="relative mx-auto max-w-lg">
              {/* Main Image Container */}
              <div className="relative rounded-2xl overflow-hidden shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500">
                <Image
                  src="https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&h=800"
                  alt="AI Generated Wedding Portrait"
                  width={600}
                  height={800}
                  className="w-full h-auto object-cover"
                  priority
                />
                
                {/* Floating Badge */}
                <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg">
                  <div className="flex items-center space-x-2">
                    <Sparkles className="w-4 h-4 text-pink-500" />
                    <span className="text-sm font-semibold text-gray-800">
                      {currentLocale === 'zh' ? 'AI生成' : 'AI Generated'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-6 -left-6 w-24 h-24 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
              
              <div className="absolute -bottom-6 -right-6 bg-white dark:bg-gray-800 rounded-2xl p-4 shadow-xl">
                <div className="text-center">
                  <div className="text-2xl font-bold text-pink-600">3min</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    {currentLocale === 'zh' ? '快速生成' : 'Quick Generate'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
