"use client";

import { Upload, Palette, Download, ArrowRight } from "lucide-react";
import { Card } from "@/components/ui/card";
import Image from "next/image";

interface HowItWorksSectionProps {
  usage: {
    title: string;
    subtitle: string;
    steps: Array<{
      title: string;
      description: string;
      image: string;
    }>;
  };
}

const stepIcons = [Upload, Palette, Download];

export default function HowItWorksSection({ usage }: HowItWorksSectionProps) {
  return (
    <section id="how-it-works" className="relative flex items-center justify-center min-h-[calc(100vh-4rem)] w-full py-12 md:py-24 lg:py-32 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              {usage.title}
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {usage.subtitle}
          </p>
        </div>

        {/* Steps */}
        <div className="relative">
          {/* Connection Line */}
          <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-purple-300 via-pink-300 to-purple-300 transform -translate-y-1/2 z-0"></div>
          
          <div className="grid lg:grid-cols-3 gap-8 relative z-10">
            {usage.steps.map((step, index) => {
              const IconComponent = stepIcons[index];
              return (
                <div key={index} className="relative">
                  <Card className="p-8 text-center hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 group hover:scale-105">
                    {/* Step Number */}
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                      {index + 1}
                    </div>

                    {/* Icon */}
                    <div className="w-20 h-20 bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                      <IconComponent className="w-10 h-10 text-purple-600 dark:text-purple-400" />
                    </div>

                    {/* Content */}
                    <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                      {step.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                      {step.description}
                    </p>

                    {/* Image Placeholder */}
                    <div className="relative w-full h-48 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 rounded-lg overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                          <IconComponent className="w-12 h-12 text-purple-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Step {index + 1} Preview
                          </p>
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* Arrow for desktop */}
                  {index < usage.steps.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-20">
                      <div className="w-8 h-8 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-lg border-2 border-purple-200 dark:border-purple-700">
                        <ArrowRight className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full font-semibold hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 shadow-lg cursor-pointer">
            <span>Ready to get started?</span>
            <ArrowRight className="w-5 h-5 ml-2" />
          </div>
        </div>
      </div>
    </section>
  );
}
