"use client";

import { useState, useRef, useCallback } from "react";
import { usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
// import { Progress } from "@/components/ui/progress";
import { 
  Upload, 
  Sparkles, 
  Download, 
  Plus, 
  X, 
  Camera,
  Heart,
  Star,
  Crown,
  Flower,
  Mountain,
  Church,
  Waves,
  TreePine,
  Castle,
  Wine,
  Building
} from "lucide-react";
import Image from "next/image";
import { useSession } from "next-auth/react";
import { signIn } from "next-auth/react";

interface AppSectionProps {
  app: {
    title: string;
    subtitle: string;
    upload: {
      title: string;
      description: string;
      dragText: string;
      supportText: string;
    };
    styles: {
      title: string;
      options: Array<{
        id: string;
        name: string;
        description: string;
        image: string;
      }>;
    };
    scenes: {
      title: string;
      options: Array<{
        id: string;
        name: string;
        description: string;
        image: string;
      }>;
    };
    controls: {
      title: string;
      lighting: {
        label: string;
        options: Array<{ value: string; label: string }>;
      };
      pose: {
        label: string;
        options: Array<{ value: string; label: string }>;
      };
      quality: {
        label: string;
        options: Array<{ value: string; label: string }>;
      };
      generateBtn: string;
    };
    loading: {
      title: string;
      steps: string[];
    };
    results: {
      title: string;
      generateMore: string;
      downloadAll: string;
    };
  };
}

const styleIcons = {
  classic: Heart,
  modern: Star,
  vintage: Crown,
  bohemian: Flower,
  princess: Crown,
  minimalist: Star
};

const sceneIcons = {
  church: Church,
  beach: Waves,
  garden: TreePine,
  castle: Castle,
  vineyard: Wine,
  ballroom: Building
};

export default function AppSection({ app }: AppSectionProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];
  
  const [currentStep, setCurrentStep] = useState<'upload' | 'style' | 'scene' | 'controls' | 'loading' | 'results'>('upload');
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [selectedStyle, setSelectedStyle] = useState<string>('');
  const [selectedScene, setSelectedScene] = useState<string>('');
  const [selectedLighting, setSelectedLighting] = useState<string>('natural');
  const [selectedPose, setSelectedPose] = useState<string>('portrait');
  const [selectedQuality, setSelectedQuality] = useState<string>('high');
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [loadingStep, setLoadingStep] = useState(0);
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = useCallback((file: File) => {
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedImage(e.target?.result as string);
        setCurrentStep('style');
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  }, [handleFileUpload]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files[0]);
    }
  }, [handleFileUpload]);

  const handleStyleSelect = (styleId: string) => {
    setSelectedStyle(styleId);
    setCurrentStep('scene');
  };

  const handleSceneSelect = (sceneId: string) => {
    setSelectedScene(sceneId);
    setCurrentStep('controls');
  };

  const handleGenerate = async () => {
    if (!session) {
      signIn();
      return;
    }

    setIsGenerating(true);
    setCurrentStep('loading');
    setProgress(0);
    setLoadingStep(0);

    // Simulate generation process
    const steps = app.loading.steps;
    for (let i = 0; i < steps.length; i++) {
      setLoadingStep(i);
      setProgress((i + 1) * (100 / steps.length));
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Simulate generated results
    const mockImages = [
      "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=600&fit=crop",
      "https://images.unsplash.com/photo-1515934751635-c81c6bc9a2d8?w=400&h=600&fit=crop",
      "https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=400&h=600&fit=crop"
    ];
    
    setGeneratedImages(mockImages);
    setIsGenerating(false);
    setCurrentStep('results');
  };

  const resetApp = () => {
    setCurrentStep('upload');
    setUploadedImage(null);
    setSelectedStyle('');
    setSelectedScene('');
    setGeneratedImages([]);
    setProgress(0);
    setLoadingStep(0);
  };

  return (
    <section id="app" className="relative flex items-center justify-center min-h-[calc(100vh-4rem)] w-full py-12 md:py-24 lg:py-32 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent mb-4">
            {app.title}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {app.subtitle}
          </p>
        </div>

        {/* App Container */}
        <div className="max-w-4xl mx-auto">
          {/* Upload Section */}
          {currentStep === 'upload' && (
            <Card className="border-2 border-dashed border-pink-300 dark:border-pink-600 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-center text-2xl font-bold text-gray-800 dark:text-white">
                  {app.upload.title}
                </CardTitle>
                <p className="text-center text-gray-600 dark:text-gray-300">
                  {app.upload.description}
                </p>
              </CardHeader>
              <CardContent>
                <div
                  className="border-2 border-dashed border-pink-300 dark:border-pink-600 rounded-lg p-12 text-center hover:border-pink-400 dark:hover:border-pink-500 transition-colors cursor-pointer"
                  onDrop={handleDrop}
                  onDragOver={(e) => e.preventDefault()}
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className="w-16 h-16 text-pink-500 mx-auto mb-4" />
                  <p className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {app.upload.dragText}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {app.upload.supportText}
                  </p>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Style Selection */}
          {currentStep === 'style' && (
            <Card className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-center text-2xl font-bold text-gray-800 dark:text-white">
                  {app.styles.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                  {app.styles.options.map((style) => {
                    const IconComponent = styleIcons[style.id as keyof typeof styleIcons] || Heart;
                    return (
                      <div
                        key={style.id}
                        className="relative group cursor-pointer rounded-lg overflow-hidden border-2 border-transparent hover:border-pink-400 transition-all duration-300"
                        onClick={() => handleStyleSelect(style.id)}
                      >
                        <div className="aspect-[3/4] relative">
                          <Image
                            src={style.image}
                            alt={style.name}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                          <div className="absolute bottom-4 left-4 right-4 text-white">
                            <div className="flex items-center mb-2">
                              <IconComponent className="w-5 h-5 mr-2" />
                              <h3 className="font-semibold">{style.name}</h3>
                            </div>
                            <p className="text-sm opacity-90">{style.description}</p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Scene Selection */}
          {currentStep === 'scene' && (
            <Card className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-center text-2xl font-bold text-gray-800 dark:text-white">
                  {app.scenes.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                  {app.scenes.options.map((scene) => {
                    const IconComponent = sceneIcons[scene.id as keyof typeof sceneIcons] || Mountain;
                    return (
                      <div
                        key={scene.id}
                        className="relative group cursor-pointer rounded-lg overflow-hidden border-2 border-transparent hover:border-pink-400 transition-all duration-300"
                        onClick={() => handleSceneSelect(scene.id)}
                      >
                        <div className="aspect-[4/3] relative">
                          <Image
                            src={scene.image}
                            alt={scene.name}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                          <div className="absolute bottom-4 left-4 right-4 text-white">
                            <div className="flex items-center mb-2">
                              <IconComponent className="w-5 h-5 mr-2" />
                              <h3 className="font-semibold">{scene.name}</h3>
                            </div>
                            <p className="text-sm opacity-90">{scene.description}</p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Controls Section */}
          {currentStep === 'controls' && (
            <Card className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-center text-2xl font-bold text-gray-800 dark:text-white">
                  {app.controls.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Lighting Control */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {app.controls.lighting.label}
                    </label>
                    <select
                      value={selectedLighting}
                      onChange={(e) => setSelectedLighting(e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                    >
                      {app.controls.lighting.options.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Pose Control */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {app.controls.pose.label}
                    </label>
                    <select
                      value={selectedPose}
                      onChange={(e) => setSelectedPose(e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                    >
                      {app.controls.pose.options.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Quality Control */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {app.controls.quality.label}
                    </label>
                    <select
                      value={selectedQuality}
                      onChange={(e) => setSelectedQuality(e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                    >
                      {app.controls.quality.options.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Generate Button */}
                <div className="text-center pt-6">
                  <Button
                    onClick={handleGenerate}
                    size="lg"
                    className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white px-12 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                  >
                    <Sparkles className="w-6 h-6 mr-3" />
                    {app.controls.generateBtn}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Loading Section */}
          {currentStep === 'loading' && (
            <Card className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <CardContent className="py-12">
                <div className="text-center space-y-6">
                  <div className="relative w-24 h-24 mx-auto">
                    <div className="absolute inset-0 border-4 border-pink-200 dark:border-pink-800 rounded-full"></div>
                    <div className="absolute inset-0 border-4 border-pink-500 rounded-full border-t-transparent animate-spin"></div>
                    <Sparkles className="absolute inset-0 w-8 h-8 m-auto text-pink-500" />
                  </div>

                  <h3 className="text-2xl font-bold text-gray-800 dark:text-white">
                    {app.loading.title}
                  </h3>

                  <p className="text-lg text-gray-600 dark:text-gray-300">
                    {app.loading.steps[loadingStep]}
                  </p>

                  <div className="max-w-md mx-auto">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-pink-500 to-purple-600 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                      {Math.round(progress)}% {currentLocale === 'zh' ? '完成' : 'Complete'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Results Section */}
          {currentStep === 'results' && (
            <Card className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-center text-2xl font-bold text-gray-800 dark:text-white">
                  {app.results.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-8">
                {/* Generated Images Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {generatedImages.map((image, index) => (
                    <div key={index} className="relative group rounded-lg overflow-hidden shadow-lg">
                      <div className="aspect-[3/4] relative">
                        <Image
                          src={image}
                          alt={`Generated wedding photo ${index + 1}`}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <Button
                            size="sm"
                            className="bg-white/90 text-gray-800 hover:bg-white"
                          >
                            <Download className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    onClick={() => setCurrentStep('controls')}
                    variant="outline"
                    size="lg"
                    className="border-pink-300 text-pink-600 hover:bg-pink-50 dark:border-pink-600 dark:text-pink-400 dark:hover:bg-pink-900/20"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    {app.results.generateMore}
                  </Button>

                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white"
                  >
                    <Download className="w-5 h-5 mr-2" />
                    {app.results.downloadAll}
                  </Button>
                </div>

                {/* Reset Button */}
                <div className="text-center pt-4">
                  <Button
                    onClick={resetApp}
                    variant="ghost"
                    className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  >
                    <Camera className="w-4 h-4 mr-2" />
                    {currentLocale === 'zh' ? '重新开始' : 'Start Over'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </section>
  );
}
