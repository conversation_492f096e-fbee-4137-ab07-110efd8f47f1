"use client";

import { Spark<PERSON>, Wand2, Palette, Shield, <PERSON>, Zap } from "lucide-react";
import { Card } from "@/components/ui/card";

interface FeaturesSectionProps {
  introduce: {
    title: string;
    subtitle: string;
    description: string;
    image: string;
    features: Array<{
      title: string;
      description: string;
      icon: string;
    }>;
  };
  benefit: {
    title: string;
    subtitle: string;
    benefits: Array<{
      title: string;
      description: string;
      icon: string;
    }>;
  };
}

const iconMap = {
  sparkles: Sparkles,
  wand: Wand2,
  palette: Palette,
  speed: Zap,
  team: Users,
  shield: Shield,
};

export default function FeaturesSection({ introduce, benefit }: FeaturesSectionProps) {
  return (
    <section id="features" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Introduce Section */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
              {introduce.title}
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-4 max-w-3xl mx-auto">
            {introduce.subtitle}
          </p>
          <p className="text-lg text-gray-500 dark:text-gray-400 max-w-4xl mx-auto">
            {introduce.description}
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-20">
          {introduce.features.map((feature, index) => {
            const IconComponent = iconMap[feature.icon as keyof typeof iconMap] || Sparkles;
            return (
              <Card key={index} className="p-8 text-center hover:shadow-lg transition-shadow duration-300 border-0 bg-gradient-to-br from-pink-50 to-purple-50 dark:from-pink-900/20 dark:to-purple-900/20">
                <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <IconComponent className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {feature.description}
                </p>
              </Card>
            );
          })}
        </div>

        {/* Benefits Section */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              {benefit.title}
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {benefit.subtitle}
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-3 gap-8">
          {benefit.benefits.map((benefitItem, index) => {
            const IconComponent = iconMap[benefitItem.icon as keyof typeof iconMap] || Sparkles;
            return (
              <Card key={index} className="p-8 hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 group hover:scale-105">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <IconComponent className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                  {benefitItem.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {benefitItem.description}
                </p>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}
